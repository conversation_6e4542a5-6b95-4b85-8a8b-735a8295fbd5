{
  "autoDocstring.docstringFormat": "numpy",
  "editor.formatOnPaste": false,
  "editor.formatOnSave": true,
  "editor.rulers": [
    120
  ],
  "files.autoSave": "onFocusChange",
  "python.testing.pytestEnabled": true,
  "python.testing.unittestEnabled": false,
  "[python]": {
    "editor.codeActionsOnSave": {
      "source.organizeImports.ruff": "explicit"
    },
    "editor.formatOnSave": true,
    "editor.snippetSuggestions": "bottom",
    "editor.suggest.localityBonus": true,
    "editor.suggest.filterGraceful": true,
    "editor.suggest.snippetsPreventQuickSuggestions": true,
    "editor.suggestSelection": "recentlyUsed"
  },
  "python.languageServer": "None", // this has to be set to None so that we can use Pyrefly functionality instead of Pylance / Default language server
}