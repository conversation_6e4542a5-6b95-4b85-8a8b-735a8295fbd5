[tool.poetry]
name = "a2110-olympus"
version = "0.1.0"
description = "Monorepo containing all projects that make up Olympus."
authors = [
    "Infrabel ICT-315 <<EMAIL>>",
]
readme = "README.md"
packages = [
    { include = "a2110_olympus", from = "src" },
    { include = "core_api", from = "src" },
    { include = "enrichment_client", from = "src" },
    { include = "fwd_scom_optic", from = "src" },
    { include = "heartbeat_job", from = "src" },
    { include = "icinga_events", from = "src" },
    { include = "icinga_objects", from = "src" },
    { include = "ing_zabbix", from = "src" },
    { include = "mail2incidents", from = "src" },
    { include = "mon_adva", from = "src" },
    { include = "mon_big_data", from = "src" },
    { include = "mon_ca_spectrum", from = "src" },
    { include = "mon_certificates", from = "src" },
    { include = "mon_cnms", from = "src" },
    { include = "mon_datalines", from = "src" },
    { include = "mon_dica", from = "src" },
    { include = "mon_gsx", from = "src" },
    { include = "mon_local6", from = "src" },
    { include = "mon_lucent_oms", from = "src" },
    { include = "mon_netact", from = "src" },
    { include = "mon_occ", from = "src" },
    { include = "mon_openshift", from = "src" },
    { include = "mon_pem", from = "src" },
    { include = "mon_scada", from = "src" },
    { include = "mon_sap_solman", from = "src" },
    { include = "mon_scom", from = "src" },
    { include = "mon_stonebranch", from = "src" },
    { include = "mon_struxureware", from = "src" },
    { include = "mon_vrealize", from = "src" },
    { include = "mon_websphere_mq", from = "src" },
    { include = "mon_zabbix", from = "src" },
    { include = "olympus_common", from = "src" },
    { include = "optic_matcher", from = "src" },
    { include = "sap_air_client", from = "src" },
    { include = "sbom_tools", from = "src" },
]

[tool.poetry.scripts]
runservice = 'a2110_olympus.run:main'
updatelaunch = 'a2110_olympus.update_launch:main'
updatepackages = 'a2110_olympus.update_packages:main'
generatedotenv = 'a2110_olympus.generate_env:main'
generatetoken = 'a2110_olympus.generate_token:main'

[[tool.poetry.source]]
name = "infrabel-artifactory"
url = "https://artifactory.msnet.railb.be/artifactory/api/pypi/pypi/simple"
priority = "primary"

[tool.poetry.dependencies]
python = "^3.11"
python-dotenv = "^1.0.1"
psycopg = { extras = ["binary"], version = "^3.2.1" }
pandas = "^2.2.2"
elasticsearch = "^8.15.0"
tabulate = "^0.9.0"
openpyxl = "^3.1.5"
icinga2apic = "^0.7.5"
pyarrow = "^17.0.0"
pymssql = "^2.3.1"
jinja2 = "^3.1.4"
requests = "^2.32.3"
fastapi = "^0.112.2"
pydantic = "2.10.2"
uvicorn = "^0.34.3"
defusedxml = "^0.7.1"
sqlalchemy = "^2.0.32"
pyyaml = "^6.0.2"
oracledb = "^2.4.1"
apscheduler = "^3.10.4"
zabbix-utils = "^2.0.0"
pyjwt = { extras = ["crypto"], version = "^2.9.0" }
cyclonedx-bom = "^5.0.0"
cachetools = "^5.5.0"
python-dateutil = "^2.9.0.post0"
elastic-apm = { version = "^6.23.0" }
annotated-types = "^0.7.0"
pydantic-core = "2.27.1"
psutil = "^7.0.0"
confluent-kafka = "^2.10.1"
reportlab = "^4.4.3"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.2"
pytest-cov = "^5.0.0"
pytest-mock = "^3.14.0"
poethepoet = "^0.28.0"
pandas-stubs = "^2.2.2.240807"
types-requests = "^2.32.0.20240712"
types-tabulate = "^0.9.0.20240106"
types-python-dateutil = "^2.9.0.20240821"
httpx = "^0.27.2"
types-defusedxml = "^0.7.0.20240218"
types-pyyaml = "^6.0.12.20240808"
pydoclint = { extras = ["flake8"], version = "0.4.1" }
freezegun = "^1.5.1"
types-cachetools = "^5.5.0.20240820"
ruff = "^0.9.3"
snakeviz = "^2.2.2"
pyrefly = "^0.31.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
line-length = 120
target-version = "py311"

[tool.ruff.lint]
select = ["B", "D", "E", "F", "FAST", "I", "N", "PT", "S", "W"]
ignore = ["S101", "E203"]

[tool.ruff.lint.isort]
split-on-trailing-comma = false

[tool.ruff.lint.per-file-ignores]
"tests/*" = ["D"]

[tool.ruff.lint.pydocstyle]
convention = "numpy"

[tool.coverage.run]
branch = true

[tool.coverage.report]
exclude_lines = ["pragma: no cover"]
omit = ["*tests*"]


[tool.pyrefly]
project-includes = ["src"]
project-excludes = [
    "tests/**",
    "**/__pycache__",
    "**/*venv/**",
]

untyped-def-behavior = "skip-and-infer-return-any"  
ignore-missing-imports = [
    "confluent_kafka.*",
    "icinga2apic.*",
    "pymssql.*",
    "zabbix_utils.*",
    "apscheduler.*",
]

[tool.pyrefly.errors]
no-matching-overload = false
missing-attribute = false
bad-override = false
bad-context-manager = false
bad-argument-type = false
not-a-type = false
invalid-type-var = false
not-iterable = false
bad-argument-count = false


[tool.poe.tasks.format-isort]
help = "Format the code using isort."
cmd = "ruff check --select I --fix src/ tests/"

[tool.poe.tasks.format-black]
help = "Format the code using ruff with settings comparable to black."
cmd = "ruff format src/ tests/"

[tool.poe.tasks.style-isort]
help = "Validate isort code style."
cmd = "ruff check --select I --diff src/ tests/"

[tool.poe.tasks.style-black]
help = "Validate black code style."
cmd = "ruff format --check --diff src/ tests/"

[tool.poe.tasks.ruff-check]
help = "Run ruff check on src and tests."
cmd = "ruff check src/ tests/"

[tool.poe.tasks.ruff-check-fix]
help = "Run ruff check with --fix enabled on src and tests."
cmd = "ruff check --fix src/ tests/"


[tool.poe.tasks.pyrefly]
help = "Run pyrefly on the source code. Tests are excluded from pyrefly checks."
cmd = "pyrefly check"


[tool.poe.tasks.upgrade-pip]
help = "Upgrade pip using poetry environment."
cmd = "poetry run pip install --upgrade pip"

[tool.poe.tasks.poetry-update]
help = "Run poetry update. This wrapper exists so it can be used in sequences"
cmd = "poetry update"

[tool.poe.tasks.upgrade-dependencies]
help = "Upgrade the package's current dependencies to their latest available version (does not respect boundaries)."
cmd = "python scripts/upgrade_dependencies.py"

[tool.poe.tasks.update]
help = "Upgrade pip and update dependencies within their boundaries."
sequence = ["upgrade-pip", "poetry-update"]

[tool.poe.tasks.upgrade]
help = "Upgrade pip and upgrade dependencies outside their boundaries."
sequence = ["upgrade-pip", "upgrade-dependencies"]

[tool.poe.tasks.format]
help = "Run formatting tools on the code base."
sequence = ["format-isort", "format-black"]

[tool.poe.tasks.fix]
help = "Run formatting tools and ruff check with fix enabled on the code base."
sequence = ["ruff-check-fix", "format-isort", "format-black"]
ignore_fail = true

[tool.poe.tasks.lint]
help = "Run all linting checks on the code base."
sequence = ["style-isort", "style-black", "ruff-check", "pyrefly"]

[tool.poe.tasks.test]
help = "Run all tests using pytest with coverage."
cmd = 'pytest -s --cov=src/ --cov-report=term-missing --cov-report html'

[tool.poe.tasks.test-e2e]
help = "Run all tests using pytest with coverage."
cmd = 'pytest --run-e2e -s --cov=src/ --cov-report=term-missing --cov-report html'

[tool.poe.tasks.clean-git-branches]
help = "Remove the local branches that were pushed to a remote, but no longer exist on that remote."
cmd = "python scripts/clean_git_branches.py"

[tool.poe.tasks.update-launch]
help = "Update .vscode/launch.json with all services."
script = "a2110_olympus.update_launch:main"

[tool.poe.tasks.update-packages]
help = "Update packages in pyproject.toml."
script = "a2110_olympus.update_packages:main"

[tool.poe.tasks.updateall]
help = "Run all update commands on the code base."
sequence = ["update-launch", "update-packages"]

[tool.poe.tasks.add-test-case]
help = "Add a test-case which can then be manually edited to verify correct output."
cmd = "python scripts/add_test_case.py"

[tool.pytest.ini_options]
markers = ["e2e: marks tests as end2end (run with '--run-e2e')"]
